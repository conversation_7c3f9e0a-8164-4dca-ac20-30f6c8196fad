package com.nnnmkj.thai.module.member.controller.admin.group;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.*;
import com.nnnmkj.thai.module.member.convert.group.MemberGroupConvert;
import com.nnnmkj.thai.module.member.dal.dataobject.group.MemberGroupDO;
import com.nnnmkj.thai.module.member.service.group.MemberGroupService;
import com.nnnmkj.thai.module.member.service.user.MemberUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 用户分组")
@RestController
@RequestMapping("/member/group")
@Validated
public class MemberGroupController {

    @Resource
    private MemberGroupService groupService;
    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private MemberUserService memberUserService;

    @PostMapping("/create")
    @Operation(summary = "创建用户分组")
    @PreAuthorize("@ss.hasPermission('member:group:create')")
    public CommonResult<Long> createGroup(@Valid @RequestBody MemberGroupCreateReqVO createReqVO) {
        return success(groupService.createGroup(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户分组")
    @PreAuthorize("@ss.hasPermission('member:group:update')")
    public CommonResult<Boolean> updateGroup(@Valid @RequestBody MemberGroupUpdateReqVO updateReqVO) {
        groupService.updateGroup(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户分组")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('member:group:delete')")
    public CommonResult<Boolean> deleteGroup(@RequestParam("id") Long id) {
        groupService.deleteGroup(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户分组")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:group:query')")
    public CommonResult<MemberGroupRespVO> getGroup(@RequestParam("id") Long id) {
        MemberGroupDO group = groupService.getGroup(id);
        return success(MemberGroupConvert.INSTANCE.convert(group));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获取会员分组精简信息列表")
    @PreAuthorize("@ss.hasPermission('member:group:query')")
    public CommonResult<List<MemberGroupSimpleRespVO>> getSimpleGroupList() {
        List<MemberGroupDO> list = groupService.getEnableGroupList();
        return success(MemberGroupConvert.INSTANCE.convertSimpleList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户分组分页")
    @PreAuthorize("@ss.hasPermission('member:group:query')")
    public CommonResult<PageResult<MemberGroupRespVO>> getGroupPage(@Valid MemberGroupPageReqVO pageVO) {
        PageResult<MemberGroupDO> pageResult = groupService.getGroupPage(pageVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        Set<Long> userIds = CollectionUtils.convertSet(pageResult.getList(), MemberGroupDO::getUserId);
        List<MemberUserRespDTO> userList = memberUserApi.getUserList(userIds);
        return success(MemberGroupConvert.INSTANCE.convertPage(pageResult, userList));
    }

    @GetMapping("/page/user")
    @Operation(summary = "查询用户分组及成员分页")
    @PreAuthorize("@ss.hasPermission('member:group:query')")
    public CommonResult<PageResult<MemberGroupUsersRespVO>> getGroupsUserPage(@Valid MemberGroupPageReqVO pageVO) {
        return success(groupService.getGroupUserPage(pageVO));
    }
}
