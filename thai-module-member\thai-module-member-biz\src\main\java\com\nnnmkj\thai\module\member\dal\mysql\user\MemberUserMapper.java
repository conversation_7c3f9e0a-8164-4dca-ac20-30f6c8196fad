package com.nnnmkj.thai.module.member.dal.mysql.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.member.controller.admin.user.vo.MemberUserPageReqVO;
import com.nnnmkj.thai.module.member.dal.dataobject.user.MemberUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员 User Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberUserMapper extends BaseMapperX<MemberUserDO> {

    default MemberUserDO selectByMobile(String mobile) {
        return selectOne(MemberUserDO::getMobile, mobile);
    }

    default List<MemberUserDO> selectListByNicknameLike(String nickname) {
        return selectList(new LambdaQueryWrapperX<MemberUserDO>()
                .likeIfPresent(MemberUserDO::getNickname, nickname));
    }

    private LambdaQueryWrapperX<MemberUserDO> buildCommonCondition(MemberUserPageReqVO reqVO) {
        LambdaQueryWrapperX<MemberUserDO> wrapper = new LambdaQueryWrapperX<MemberUserDO>()
                .likeIfPresent(MemberUserDO::getMobile, reqVO.getMobile())
                .betweenIfPresent(MemberUserDO::getLoginDate, reqVO.getLoginDate())
                .likeIfPresent(MemberUserDO::getNickname, reqVO.getNickname())
                .betweenIfPresent(MemberUserDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(MemberUserDO::getLevelId, reqVO.getLevelId());

        // 标签 ID 多选（FIND_IN_SET）
        if (CollUtil.isNotEmpty(reqVO.getTagIds())) {
            String tagSql = reqVO.getTagIds().stream()
                    .map(tagId -> "FIND_IN_SET(" + tagId + ", tag_ids)")
                    .collect(Collectors.joining(" OR "));
            wrapper.apply(StrUtil.isNotEmpty(tagSql), tagSql);
        }

        // 分组 ID 多选（FIND_IN_SET）
        if (CollUtil.isNotEmpty(reqVO.getGroupIds())) {
            String groupSql = reqVO.getGroupIds().stream()
                    .map(groupId -> "FIND_IN_SET(" + groupId + ", group_ids)")
                    .collect(Collectors.joining(" OR "));
            wrapper.apply(StrUtil.isNotEmpty(groupSql), groupSql);
        }

        // 倒序排列
        wrapper.orderByDesc(MemberUserDO::getId);
        return wrapper;
    }

    default PageResult<MemberUserDO> selectPage(MemberUserPageReqVO reqVO) {
        LambdaQueryWrapperX<MemberUserDO> queryWrapper = buildCommonCondition(reqVO);
        return selectPage(reqVO, queryWrapper);
    }

    default List<MemberUserDO> selectList(MemberUserPageReqVO reqVO) {
        LambdaQueryWrapperX<MemberUserDO> queryWrapper = buildCommonCondition(reqVO);
        return selectList(queryWrapper);
    }

    default Long selectCountByGroupId(Long groupId) {
        return selectCount(new LambdaQueryWrapperX<MemberUserDO>()
                .apply("FIND_IN_SET({0}, group_ids)", groupId));
    }

    default Long selectCountByLevelId(Long levelId) {
        return selectCount(MemberUserDO::getLevelId, levelId);
    }

    default Long selectCountByTagId(Long tagId) {
        return selectCount(new LambdaQueryWrapperX<MemberUserDO>()
                .apply("FIND_IN_SET({0}, tag_ids)", tagId));
    }

    /**
     * 更新用户积分（增加）
     *
     * @param id        用户编号
     * @param incrCount 增加积分（正数）
     */
    default void updatePointIncr(Long id, Integer incrCount) {
        Assert.isTrue(incrCount > 0);
        LambdaUpdateWrapper<MemberUserDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<MemberUserDO>()
                .setSql(" point = point + " + incrCount)
                .eq(MemberUserDO::getId, id);
        update(null, lambdaUpdateWrapper);
    }

    /**
     * 更新用户积分（减少）
     *
     * @param id        用户编号
     * @param incrCount 增加积分（负数）
     * @return 更新行数
     */
    default int updatePointDecr(Long id, Integer incrCount) {
        Assert.isTrue(incrCount < 0);
        LambdaUpdateWrapper<MemberUserDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<MemberUserDO>()
                .setSql(" point = point + " + incrCount) // 负数，所以使用 + 号
                .eq(MemberUserDO::getId, id);
        return update(null, lambdaUpdateWrapper);
    }

    /**
     * 根据用户编号和分组编号查询用户信息
     *
     * @param groupId 分组编号
     * @param userId  用户编号
     * @return 匹配的用户数据对象
     */
    default List<MemberUserDO> selectListByUserIdAndGroupId(Long groupId, Long userId) {
        return selectList(new LambdaQueryWrapperX<MemberUserDO>()
                .eq(MemberUserDO::getId, userId)
                .apply("FIND_IN_SET({0}, group_ids)", groupId)
                .orderByDesc(MemberUserDO::getId));
    }

    /**
     * 根据用户编号和分组编号列表查询用户信息
     *
     * @param groupIds 分组编号列表
     * @param userId   用户编号
     * @return 匹配的用户数据对象
     */
    default List<MemberUserDO> selectListByUserIdAndGroupIds(Collection<Long> groupIds, Long userId) {
        String groupSql = groupIds.stream()
                .map(groupId -> "FIND_IN_SET(" + groupId + ", group_ids)")
                .collect(Collectors.joining(" OR "));
        if (CollUtil.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<MemberUserDO>()
                .eq(MemberUserDO::getId, userId)
                .apply(groupSql)
                .orderByDesc(MemberUserDO::getId));
    }

    /**
     * 清空用户关联的系统用户ID
     *
     * @param id 用户编号
     * @return 更新行数
     */
    @Update("UPDATE member_user SET system_user_id = NULL WHERE id = ${id}")
    int clearSystemUserIdById(@Param("id") Long id);

}
