package com.nnnmkj.thai.module.member.convert.user;

import cn.hutool.core.bean.BeanUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import com.nnnmkj.thai.module.member.controller.admin.user.vo.MemberUserBaseVO;
import com.nnnmkj.thai.module.member.controller.admin.user.vo.MemberUserRespVO;
import com.nnnmkj.thai.module.member.controller.admin.user.vo.MemberUserUpdateReqVO;
import com.nnnmkj.thai.module.member.controller.app.user.vo.AppMemberUserInfoRespVO;
import com.nnnmkj.thai.module.member.controller.app.user.vo.AppMemberUserInfoRespVO.Level;
import com.nnnmkj.thai.module.member.convert.address.AddressConvert;
import com.nnnmkj.thai.module.member.dal.dataobject.certification.CertificationDO;
import com.nnnmkj.thai.module.member.dal.dataobject.group.MemberGroupDO;
import com.nnnmkj.thai.module.member.dal.dataobject.level.MemberLevelDO;
import com.nnnmkj.thai.module.member.dal.dataobject.tag.MemberTagDO;
import com.nnnmkj.thai.module.member.dal.dataobject.user.MemberUserDO;
import com.nnnmkj.thai.module.member.enums.CertificationStatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

import static com.nnnmkj.thai.framework.common.util.collection.CollectionUtils.convertMap;

@Mapper(uses = {AddressConvert.class})
public interface MemberUserConvert {

    MemberUserConvert INSTANCE = Mappers.getMapper(MemberUserConvert.class);

    MemberUserDO convert(MemberUserUpdateReqVO bean);

    AppMemberUserInfoRespVO convert(MemberUserDO bean);

    default AppMemberUserInfoRespVO convert(MemberUserDO bean, MemberLevelDO level, CertificationDO certification) {
        AppMemberUserInfoRespVO infoRespVO = BeanUtil.toBean(bean, AppMemberUserInfoRespVO.class);
        infoRespVO.setLevel(BeanUtil.toBean(level, Level.class));
        infoRespVO.setIsCertified(certification != null && CertificationStatusEnum.isApproved(certification.getStatus()));
        return infoRespVO;
    }

    MemberUserRespDTO convert2(MemberUserDO bean);

    @Mapping(source = "areaId", target = "areaName", qualifiedByName = "convertAreaIdToAreaName")
    MemberUserRespVO convert03(MemberUserDO bean);

    PageResult<MemberUserRespVO> convertPage(PageResult<MemberUserDO> page);

    default PageResult<MemberUserRespVO> convertPage(PageResult<MemberUserDO> pageResult, List<MemberTagDO> tags, List<MemberLevelDO> levels, List<MemberGroupDO> groups) {
        PageResult<MemberUserRespVO> result = convertPage(pageResult);
        // 处理关联数据
        Map<Long, String> tagMap = convertMap(tags, MemberTagDO::getId, MemberTagDO::getName);
        Map<Long, String> levelMap = convertMap(levels, MemberLevelDO::getId, MemberLevelDO::getName);
        Map<Long, String> groupMap = convertMap(groups, MemberGroupDO::getId, MemberGroupDO::getName);
        // 填充关联数据
        result.getList().forEach(user -> {
            user.setTagNames(CollectionUtils.convertList(user.getTagIds(), tagMap::get));
            user.setLevelName(levelMap.get(user.getLevelId()));
            user.setGroupNames(CollectionUtils.convertList(user.getGroupIds(), groupMap::get));
        });
        return result;
    }

    List<MemberUserRespDTO> convertList(List<MemberUserDO> list);

    List<MemberUserBaseVO> convertList2(List<MemberUserDO> list);

}
