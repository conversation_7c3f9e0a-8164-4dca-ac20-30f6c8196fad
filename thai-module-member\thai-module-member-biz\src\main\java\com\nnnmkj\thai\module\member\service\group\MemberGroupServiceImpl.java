package com.nnnmkj.thai.module.member.service.group;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.nnnmkj.thai.framework.common.enums.CommonStatusEnum;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.datapermission.core.util.DataPermissionUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupCreateReqVO;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupPageReqVO;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupUpdateReqVO;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupUsersRespVO;
import com.nnnmkj.thai.module.member.controller.admin.user.vo.MemberUserPageReqVO;
import com.nnnmkj.thai.module.member.convert.group.MemberGroupConvert;
import com.nnnmkj.thai.module.member.convert.user.MemberUserConvert;
import com.nnnmkj.thai.module.member.dal.dataobject.group.MemberGroupDO;
import com.nnnmkj.thai.module.member.dal.dataobject.user.MemberUserDO;
import com.nnnmkj.thai.module.member.dal.mysql.group.MemberGroupMapper;
import com.nnnmkj.thai.module.member.dal.mysql.user.MemberUserMapper;
import com.nnnmkj.thai.module.member.enums.ErrorCodeConstants;
import com.nnnmkj.thai.module.member.service.user.MemberUserService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.member.enums.ErrorCodeConstants.GROUP_HAS_USER;
import static com.nnnmkj.thai.module.member.enums.ErrorCodeConstants.GROUP_NOT_EXISTS;

/**
 * 用户分组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberGroupServiceImpl implements MemberGroupService {

    @Resource
    private MemberGroupMapper memberGroupMapper;
    @Resource
    private MemberUserService memberUserService;
    @Resource
    private MemberUserMapper memberUserMapper;
    @Resource
    @Lazy
    private MemberUserApi memberUserApi;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createGroup(MemberGroupCreateReqVO createReqVO) {
        // 插入
        MemberGroupDO group = MemberGroupConvert.INSTANCE.convert(createReqVO);
        memberGroupMapper.insert(group);
        Long groupId = group.getId();

        // 更新用户分组
        MemberUserDO user = memberUserMapper.selectById(createReqVO.getUserId());
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        List<Long> userGroupIds = Optional.ofNullable(user.getGroupIds())
                .orElseGet(ArrayList::new);

        // 校验是否已存在
        if (!userGroupIds.contains(groupId)) {
            userGroupIds.add(groupId);
            user.setGroupIds(userGroupIds);
            memberUserMapper.updateById(user);
        }

        // 返回
        return groupId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGroup(MemberGroupUpdateReqVO updateReqVO) {
        // 校验存在
        MemberGroupDO existingGroup = memberGroupMapper.selectById(updateReqVO.getId());
        if (existingGroup == null) {
            throw exception(GROUP_NOT_EXISTS);
        }

        // 更新用户的分组字段
        Long originalUserId = existingGroup.getUserId();
        MemberUserDO user = memberUserMapper.selectById(originalUserId);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        List<Long> groupIds = user.getGroupIds();
        if (CollUtil.isNotEmpty(groupIds)) {
            List<Long> list = CollUtil.removeAny(groupIds, originalUserId);
            user.setGroupIds(list);
            memberUserMapper.updateById(user);
        }

        // 更新
        MemberGroupDO updateObj = MemberGroupConvert.INSTANCE.convert(updateReqVO);
        memberGroupMapper.updateById(updateObj);
    }

    @Override
    public void deleteGroup(Long id) {
        // 校验存在
        validateGroupExists(id);
        // 校验分组下是否有用户
        validateGroupHasUser(id);
        // 删除
        memberGroupMapper.deleteById(id);
    }

    void validateGroupExists(Long id) {
        if (memberGroupMapper.selectById(id) == null) {
            throw exception(GROUP_NOT_EXISTS);
        }
    }

    void validateGroupHasUser(Long id) {
        Long count = memberUserService.getUserCountByGroupId(id);
        if (count > 0) {
            throw exception(GROUP_HAS_USER);
        }
    }

    @Override
    public MemberGroupDO getGroup(Long id) {
        return memberGroupMapper.selectById(id);
    }

    @Override
    public List<MemberGroupDO> getGroupListByUserId(Long userId) {
        LambdaQueryWrapperX<MemberGroupDO> wrapperX = new LambdaQueryWrapperX<MemberGroupDO>()
                .eq(MemberGroupDO::getUserId, userId)
                .eq(MemberGroupDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        return memberGroupMapper.selectList(wrapperX);
    }

    @Override
    public List<MemberGroupDO> getGroupList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ListUtil.empty();
        }
        return memberGroupMapper.selectByIds(ids);
    }

    @Override
    public PageResult<MemberGroupDO> getGroupPage(MemberGroupPageReqVO pageReqVO) {
        return memberGroupMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MemberGroupDO> getGroupListByStatus(Integer status) {
        return memberGroupMapper.selectListByStatus(status);
    }

    @Override
    public void validateGroup(Long id) {
        validateGroupExists(id);
    }

    public List<MemberGroupUsersRespVO> fillGroupUsersInfo(List<MemberGroupDO> groupList) {
        // 查询分组创建者的用户信息
        Set<Long> creatorUserIds = CollectionUtils.convertSet(groupList, MemberGroupDO::getUserId);
        List<MemberUserRespDTO> creatorUserList = memberUserApi.getUserList(creatorUserIds);

        // 创建用户ID到用户信息的映射，提高查询效率
        Map<Long, MemberUserRespDTO> creatorUserMap = CollectionUtils.convertMap(creatorUserList, MemberUserRespDTO::getId);

        // 转换分组信息
        List<MemberGroupUsersRespVO> groupUsersVOList = new ArrayList<>(groupList.size());
        for (MemberGroupDO group : groupList) {
            MemberGroupUsersRespVO vo = BeanUtils.toBean(group, MemberGroupUsersRespVO.class);
            // 设置创建者昵称
            MemberUserRespDTO creator = creatorUserMap.get(group.getUserId());
            if (creator != null) {
                vo.setNickname(creator.getNickname());
            }
            groupUsersVOList.add(vo);
        }

        // 批量查询所有分组下的用户信息
        List<Long> groupIds = CollectionUtils.convertList(groupList, MemberGroupDO::getId);
        MemberUserPageReqVO reqVO = new MemberUserPageReqVO();
        reqVO.setGroupIds(groupIds);
        reqVO.setPageSize(PageParam.PAGE_SIZE_NONE);

        PageResult<MemberUserDO> allGroupUsers = DataPermissionUtils.executeIgnore(() -> memberUserService.getUserPage(reqVO));
        if (CollUtil.isEmpty(allGroupUsers.getList())) {
            return groupUsersVOList;
        }

        List<MemberUserDO> filterGroupUsers = CollectionUtils.filterList(allGroupUsers.getList(), u -> Objects.nonNull(u.getGroupIds()));

        // 构建分组ID到用户列表的映射关系
        Map<Long, List<MemberUserDO>> groupIdUserMap = new HashMap<>();
        for (MemberUserDO user : filterGroupUsers) {
            if (user.getGroupIds() != null) {
                for (Long groupId : user.getGroupIds()) {
                    if (groupId != null) {
                        groupIdUserMap.computeIfAbsent(groupId, k -> new ArrayList<>()).add(user);
                    }
                }
            }
        }

        // 为每个分组设置对应的用户列表
        for (MemberGroupUsersRespVO groupUsersVO : groupUsersVOList) {
            List<MemberUserDO> groupUserList = groupIdUserMap.getOrDefault(groupUsersVO.getId(), Collections.emptyList());
            groupUsersVO.setUsers(MemberUserConvert.INSTANCE.convertList2(groupUserList));
        }

        return groupUsersVOList;
    }

    @Override
    public PageResult<MemberGroupUsersRespVO> getGroupUserPage(MemberGroupPageReqVO pageVO) {
        // 获取分组列表
        PageResult<MemberGroupDO> pageResult = getGroupPage(pageVO);
        List<MemberGroupDO> groupList = pageResult.getList();

        if (CollUtil.isEmpty(groupList)) {
            return PageResult.empty();
        }

        // 填充分组用户信息
        List<MemberGroupUsersRespVO> groupUsersVOList = fillGroupUsersInfo(groupList);

        return new PageResult<>(groupUsersVOList, pageResult.getTotal());
    }

}
