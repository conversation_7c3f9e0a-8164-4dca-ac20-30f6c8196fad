package com.nnnmkj.thai.module.course.controller.app.lesson;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.*;
import com.nnnmkj.thai.module.course.convert.lesson.LessonConvert;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonAttachmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import com.nnnmkj.thai.module.course.service.lesson.AppLessonService;
import com.nnnmkj.thai.module.course.service.lessoncollection.AppLessonCollectionService;
import com.nnnmkj.thai.module.course.service.membergrouplesson.MemberGroupLessonService;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils.getLoginUserType;

@Tag(name = "用户 App - 课程")
@RestController
@RequestMapping("/course/lesson")
@Validated
public class AppLessonController {

    @Resource
    private AppLessonService lessonService;

    @Resource
    private MemberUserApi memberUserApi;
    
    @Resource
    private MemberGroupLessonService memberGroupLessonService;
    @Resource
    private AppLessonCollectionService lessonCollectionService;

    @PostMapping("/create")
    @Operation(summary = "创建课程")
    public CommonResult<Long> createLesson(@Valid @RequestBody AppLessonSaveReqVO createReqVO) {
        return success(lessonService.createLesson(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程")
    public CommonResult<Boolean> updateLesson(@Valid @RequestBody AppLessonSaveReqVO updateReqVO) {
        lessonService.updateLesson(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteLesson(@RequestParam("id") Long id) {
        lessonService.deleteLesson(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppLessonRespVO> getLesson(@RequestParam("id") Long id) {
        LessonDO lesson = lessonService.getLesson(id);
        AppLessonRespVO respVO = BeanUtils.toBean(lesson, AppLessonRespVO.class);
        
        // 查询当前用户是否收藏了该课程
        boolean isCollected = lessonCollectionService.isUserCollectedLesson(id, getLoginUserId(), getLoginUserType());
        respVO.setIsStore(isCollected);

        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程分页")
    public CommonResult<PageResult<AppLessonRespVO>> getLessonPage(@Valid AppLessonPageReqVO pageReqVO) {
        PageResult<LessonDO> pageResult = lessonService.getLessonPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        // 处理用户昵称返显
        List<Long> systemUserIds = CommonUtils.getUserIdsByBaseDOS(pageResult.getList(), UserTypeEnum.ADMIN);
        if (CollUtil.isEmpty(systemUserIds)) {
            return success(LessonConvert.INSTANCE.convertPage1(pageResult));
        }
        List<MemberUserRespDTO> userList = memberUserApi.getUserListBySystemUserIds(systemUserIds);
        return success(LessonConvert.INSTANCE.convertPage2(pageResult, userList));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonExcel(@Valid AppLessonPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonDO> list = lessonService.getLessonPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程.xls", "数据", AppLessonRespVO.class,
                        BeanUtils.toBean(list, AppLessonRespVO.class));
    }

    // ==================== 子表（课程附件） ====================

    @GetMapping("/lesson-attachment/list-by-course-id")
    @Operation(summary = "获得课程附件列表")
    @Parameter(name = "courseId", description = "课程ID")
    public CommonResult<List<LessonAttachmentDO>> getLessonAttachmentListByCourseId(@RequestParam("courseId") Long courseId) {
        return success(lessonService.getLessonAttachmentListByCourseId(courseId));
    }
    
    @GetMapping("/lesson-attachment/search")
    @Operation(summary = "搜索课程附件")
    @Parameter(name = "keyword", description = "搜索关键词", required = true)
    @Parameter(name = "courseId", description = "课程ID")
    public CommonResult<List<LessonAttachmentDO>> searchLessonAttachments(
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "courseId", required = false) Long courseId) {
        return success(lessonService.searchLessonAttachmentsByTitle(keyword, courseId));
    }
    
    @GetMapping("/list-by-group-id")
    @Operation(summary = "获得班级关联的课程列表")
    @Parameter(name = "groupId", description = "班级ID", required = true)
    public CommonResult<List<AppLessonRespVO>> getLessonListByGroupId(@RequestParam("groupId") Long groupId) {
        // 1. 获取班级关联的课程ID列表
        List<MemberGroupLessonDO> memberGroupLessons = memberGroupLessonService.getMemberGroupLessonListByGroupId(groupId);
        if (CollUtil.isEmpty(memberGroupLessons)) {
            return success(CollUtil.newArrayList());
        }
        
        // 2. 获取课程ID集合
        Set<Long> courseIds = memberGroupLessons.stream()
                .map(MemberGroupLessonDO::getCourseId)
                .collect(Collectors.toSet());
        
        // 3. 查询课程信息
        List<LessonDO> lessons = lessonService.getLessonListByIds(courseIds);
        if (CollUtil.isEmpty(lessons)) {
            return success(CollUtil.newArrayList());
        }
        
        // 4. 处理用户昵称返显
        List<Long> systemUserIds = CommonUtils.getUserIdsByBaseDOS(lessons, UserTypeEnum.ADMIN);
        List<MemberUserRespDTO> userList = memberUserApi.getUserListBySystemUserIds(systemUserIds);

        // 5. 处理是否收藏返回值
        List<LessonCollectionDO> lessonCollectionList = lessonCollectionService.getLessonCollectionList(getLoginUserId(), getLoginUserType(), courseIds);
        Set<Long> storeSetIds = CollectionUtils.convertSet(lessonCollectionList, LessonCollectionDO::getLessonId);

        // 6. 转换为 VO 返回
        List<AppLessonRespVO> respVOList = LessonConvert.INSTANCE.convertList(lessons, userList);
        respVOList.forEach(vo -> vo.setIsStore(storeSetIds.contains(vo.getId())));
        return success(respVOList);
    }
    
    @GetMapping("/page/created")
    @Operation(summary = "获得我创建的课程列表")
    public CommonResult<List<AppLessonRespVO>> getCreatedLessonList() {
        // 查询用户创建的课程
        List<LessonDO> lessons = lessonService.getLessonListByUserId(getLoginUserId());
        if (CollUtil.isEmpty(lessons)) {
            return success(CollUtil.newArrayList());
        }
        
        // 处理用户昵称返显
        List<Long> systemUserIds = CommonUtils.getUserIdsByBaseDOS(lessons, UserTypeEnum.ADMIN);
        List<MemberUserRespDTO> userList = memberUserApi.getUserListBySystemUserIds(systemUserIds);

        // 处理是否收藏返回值
        Set<Long> lessonIds = lessons.stream().map(LessonDO::getId).collect(Collectors.toSet());
        List<LessonCollectionDO> lessonCollectionList = lessonCollectionService.getLessonCollectionList(getLoginUserId(), getLoginUserType(), lessonIds);
        Set<Long> storeSetIds = CollectionUtils.convertSet(lessonCollectionList, LessonCollectionDO::getLessonId);

        // 转换为 VO 返回
        List<AppLessonRespVO> respVOList = LessonConvert.INSTANCE.convertList(lessons, userList);
        respVOList.forEach(vo -> vo.setIsStore(storeSetIds.contains(vo.getId())));
        return success(respVOList);
    }
    
    @GetMapping("/page/favourite")
    @Operation(summary = "获得我收藏的课程分页")
    public CommonResult<PageResult<AppLessonRespVO>> getFavoriteLessonPage(@Valid AppLessonFavoritePageReqVO pageReqVO) {
        pageReqVO.setUserId(getLoginUserId());
        PageResult<LessonDO> pageResult = lessonService.getFavoriteLessonPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        
        // 处理用户昵称返显
        List<Long> systemUserIds = CommonUtils.getUserIdsByBaseDOS(pageResult.getList(), UserTypeEnum.ADMIN);
        List<MemberUserRespDTO> userList = memberUserApi.getUserListBySystemUserIds(systemUserIds);
        
        // 所有课程都是已收藏的
        List<AppLessonRespVO> respVOList = LessonConvert.INSTANCE.convertList(pageResult.getList(), userList);
        respVOList.forEach(vo -> vo.setIsStore(true)); // 收藏页面的课程都是已收藏的
        
        return success(new PageResult<>(respVOList, pageResult.getTotal()));
    }
    
    @GetMapping("/list/joined")
    @Operation(summary = "获得我加入的班级关联的课程列表")
    public CommonResult<List<AppLessonRespVO>> getJoinedLessonList() {
        // 1. 获取用户加入的班级列表
        List<Long> groupIds = memberUserApi.getUserGroups(getLoginUserId());
        if (CollUtil.isEmpty(groupIds)) {
            return success(CollUtil.newArrayList());
        }
        
        // 2. 获取班级关联的课程ID集合
        Set<Long> courseIds = CollUtil.newHashSet();
        for (Long groupId : groupIds) {
            List<MemberGroupLessonDO> memberGroupLessons = memberGroupLessonService.getMemberGroupLessonListByGroupId(groupId);
            if (CollUtil.isNotEmpty(memberGroupLessons)) {
                Set<Long> ids = CollectionUtils.convertSet(memberGroupLessons, MemberGroupLessonDO::getCourseId);
                courseIds.addAll(ids);
            }
        }
        
        if (CollUtil.isEmpty(courseIds)) {
            return success(CollUtil.newArrayList());
        }
        
        // 3. 查询课程信息
        List<LessonDO> lessons = lessonService.getLessonListByIds(courseIds);
        if (CollUtil.isEmpty(lessons)) {
            return success(CollUtil.newArrayList());
        }
        
        // 4. 处理用户昵称返显
        List<Long> systemUserIds = CommonUtils.getUserIdsByBaseDOS(lessons, UserTypeEnum.ADMIN);
        List<MemberUserRespDTO> userList = memberUserApi.getUserListBySystemUserIds(systemUserIds);
        
        // 5. 处理是否收藏返回值
        List<LessonCollectionDO> lessonCollectionList = lessonCollectionService.getLessonCollectionList(getLoginUserId(), getLoginUserType(), courseIds);
        Set<Long> storeSetIds = CollectionUtils.convertSet(lessonCollectionList, LessonCollectionDO::getLessonId);

        // 6. 转换为 VO 返回
        List<AppLessonRespVO> respVOList = LessonConvert.INSTANCE.convertList(lessons, userList);
        respVOList.forEach(vo -> vo.setIsStore(storeSetIds.contains(vo.getId())));
        return success(respVOList);
    }
    
    @PostMapping("/search")
    @Operation(summary = "搜索课程")
    public CommonResult<PageResult<AppLessonRespVO>> searchLessons(@Valid @RequestBody AppLessonSearchReqVO reqVO) {
        // 1. 调用service层方法进行搜索
        PageResult<LessonDO> pageResult = lessonService.searchLessons(reqVO, getLoginUserId());
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        
        // 2. 处理用户昵称返显
        List<Long> systemUserIds = CommonUtils.getUserIdsByBaseDOS(pageResult.getList(), UserTypeEnum.ADMIN);
        List<MemberUserRespDTO> userList = memberUserApi.getUserListBySystemUserIds(systemUserIds);

        // 3. 处理是否收藏返回值
        Set<Long> lessonIds = CollectionUtils.convertSet(pageResult.getList(), LessonDO::getId);
        Set<Long> storeSetIds = CollUtil.newHashSet();
        if (reqVO.getType() == 2) {
            // 如果是收藏类型，所有课程都是已收藏的
            storeSetIds.addAll(lessonIds);
        } else {
            // 其他类型需要查询是否收藏
            List<LessonCollectionDO> lessonCollectionList = lessonCollectionService.getLessonCollectionList(getLoginUserId(), getLoginUserType(), lessonIds);
            storeSetIds = CollectionUtils.convertSet(lessonCollectionList, LessonCollectionDO::getLessonId);
        }
        
        // 4. 转换为 VO 返回
        List<AppLessonRespVO> respVOList = LessonConvert.INSTANCE.convertList(pageResult.getList(), userList);
        Set<Long> finalStoreSetIds = storeSetIds;
        respVOList.forEach(vo -> vo.setIsStore(finalStoreSetIds.contains(vo.getId())));
        
        return success(new PageResult<>(respVOList, pageResult.getTotal()));
    }

    @DeleteMapping("/delete-attachment")
    @Operation(summary = "删除课程附件")
    public CommonResult<Boolean> deleteLessonAttachment(@RequestParam("id") Long id){
        lessonService.deleteLessonAttachmentById(id);
        return success(true);
    }

    @PutMapping("/update-attachment")
    @Operation(summary = "更新课程附件名")
    public CommonResult<Boolean> updateLessonAttachment(@RequestParam("id") Long id,@RequestParam("title") String title){
        lessonService.updateLessonAttachmentTitle(id,title);
        return success(true);
    }
}