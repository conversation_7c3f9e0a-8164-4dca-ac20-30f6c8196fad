package com.nnnmkj.thai.module.course.controller.app.lessoncollection;

import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.module.course.controller.app.lessoncollection.vo.AppLessonCollectionSaveReqVO;
import com.nnnmkj.thai.module.course.service.lessoncollection.AppLessonCollectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils.getLoginUserType;

@Tag(name = "用户APP - 课程收藏")
@RestController
@RequestMapping("/course/lesson-collection")
@Validated
public class AppLessonCollectionController {

    @Resource
    private AppLessonCollectionService lessonCollectionService;

    @PostMapping("/create")
    @Operation(summary = "创建课程收藏")
    public CommonResult<Long> createLessonCollection(@Valid @RequestBody AppLessonCollectionSaveReqVO createReqVO) {
        return success(lessonCollectionService.createLessonCollection(createReqVO));
    }

    @PostMapping("/cancel")
    @Operation(summary = "取消课程收藏")
    @Parameter(name = "id", description = "课程编号", required = true)
    public CommonResult<Boolean> cancelLessonCollection(@RequestParam("id") Long lessonId) {
        lessonCollectionService.deleteLessonCollection(getLoginUserId(), getLoginUserType(), lessonId);
        return success(true);
    }
}