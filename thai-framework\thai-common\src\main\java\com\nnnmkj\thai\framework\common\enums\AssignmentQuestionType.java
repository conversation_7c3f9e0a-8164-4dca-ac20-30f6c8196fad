package com.nnnmkj.thai.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AssignmentQuestionType {

    JUDGE(0, "判断题"),
    RADIO(1, "单选题"),
    MULTIPLE_CHOICES(2, "多选题"),
    BLANKS(3, "填空题"),
    SHORTENER(4, "简答题"),
    TRANSLATION(5, "互译题");

    private final int code;
    private final String description;

    public static AssignmentQuestionType fromCode(int code) {
        for (AssignmentQuestionType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

}
