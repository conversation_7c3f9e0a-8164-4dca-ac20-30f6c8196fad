package com.nnnmkj.thai.module.member.service.group;

import com.nnnmkj.thai.framework.common.enums.CommonStatusEnum;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupCreateReqVO;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupPageReqVO;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupUpdateReqVO;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupUsersRespVO;
import com.nnnmkj.thai.module.member.dal.dataobject.group.MemberGroupDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 用户分组 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberGroupService {

    /**
     * 创建用户分组
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGroup(@Valid MemberGroupCreateReqVO createReqVO);

    /**
     * 更新用户分组
     *
     * @param updateReqVO 更新信息
     */
    void updateGroup(@Valid MemberGroupUpdateReqVO updateReqVO);

    /**
     * 删除用户分组
     *
     * @param id 编号
     */
    void deleteGroup(Long id);

    /**
     * 获得用户分组
     *
     * @param id 编号
     * @return 用户分组
     */
    MemberGroupDO getGroup(Long id);

    /**
     * 获得用户分组列表
     *
     * @param userId 用户编号
     * @return 用户分组列表
     */
    List<MemberGroupDO> getGroupListByUserId(Long userId);

    /**
     * 获得用户分组列表
     *
     * @param ids 编号
     * @return 用户分组列表
     */
    List<MemberGroupDO> getGroupList(Collection<Long> ids);

    /**
     * 获得用户分组分页
     *
     * @param pageReqVO 分页查询
     * @return 用户分组分页
     */
    PageResult<MemberGroupDO> getGroupPage(MemberGroupPageReqVO pageReqVO);

    /**
     * 获得指定状态的用户分组列表
     *
     * @param status 状态
     * @return 用户分组列表
     */
    List<MemberGroupDO> getGroupListByStatus(Integer status);

    /**
     * 获得开启状态的用户分组列表
     *
     * @return 用户分组列表
     */
    default List<MemberGroupDO> getEnableGroupList() {
        return getGroupListByStatus(CommonStatusEnum.ENABLE.getStatus());
    }

    /**
     * 校验分组是否存在
     *
     * @param id 分组编号
     */
    void validateGroup(Long id);

    /**
     * 填充分组的用户信息
     *
     * @param groupList 分组列表
     * @return 带用户信息的分组列表
     */
    List<MemberGroupUsersRespVO> fillGroupUsersInfo(List<MemberGroupDO> groupList);

    /**
     * 获取用户分组及成员分页
     *
     * @param pageReqVO 分页查询
     * @return 用户分组及成员分页
     */
    PageResult<MemberGroupUsersRespVO> getGroupUserPage(MemberGroupPageReqVO pageReqVO);

}
