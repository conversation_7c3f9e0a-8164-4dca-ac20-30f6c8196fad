package com.nnnmkj.thai.module.system.framework.datapermission.config;

import com.nnnmkj.thai.module.system.dal.dataobject.dept.DeptDO;
import com.nnnmkj.thai.module.system.dal.dataobject.user.AdminUserDO;
import com.nnnmkj.thai.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * system 模块的数据权限 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class DataPermissionConfiguration {

    @Bean
    public DeptDataPermissionRuleCustomizer sysDeptDataPermissionRuleCustomizer() {
        return rule -> {
            // dept
            rule.addDeptColumn(AdminUserDO.class);
            rule.addDeptColumn(DeptDO.class, "id");
            // user
            rule.addUserColumn(AdminUserDO.class, "id");

            // ================= creator =================
            // member
            rule.addCreatorColumn("member_group");
            // course
            rule.addCreatorColumn("course_assignment");
            rule.addCreatorColumn("course_assignment_answer_paper");
            rule.addCreatorColumn("course_assignment_answer_record");
            rule.addCreatorColumn("course_assignment_question");
            rule.addCreatorColumn("course_assignment_question_option");
            rule.addCreatorColumn("course_assignment_release");
            rule.addCreatorColumn("course_assignment_release_question");
            rule.addCreatorColumn("course_assignment_release_question_option");
            rule.addCreatorColumn("course_assignment_score");
            rule.addCreatorColumn("course_lesson");
            rule.addCreatorColumn("course_lesson_attachment");
            rule.addCreatorColumn("course_lesson_chapter");
            rule.addCreatorColumn("course_lesson_chapter_process");
            rule.addCreatorColumn("course_lesson_chapter_study_record");
            rule.addCreatorColumn("course_lesson_chatper_attachment");
            rule.addCreatorColumn("course_lesson_collection");
            rule.addCreatorColumn("course_lesson_process");
            rule.addCreatorColumn("course_member_group_assignment_release");
            rule.addCreatorColumn("course_member_group_lesson");
            // exam
            rule.addCreatorColumn("exam_paper");
            rule.addCreatorColumn("exam_paper_question");
            rule.addCreatorColumn("exam_question");
            rule.addCreatorColumn("exam_question_option");
            rule.addCreatorColumn("exam_question_generation_stats");
            // learning
            rule.addCreatorColumn("learning_member_group_word_set");
            rule.addCreatorColumn("learning_question");
            rule.addCreatorColumn("learning_question_option");
            rule.addCreatorColumn("learning_session");
            rule.addCreatorColumn("learning_session_card_record");
            rule.addCreatorColumn("learning_session_match_record");
            rule.addCreatorColumn("learning_session_progress");
            rule.addCreatorColumn("learning_session_question");
            rule.addCreatorColumn("learning_session_result");
            rule.addCreatorColumn("learning_session_study_record");
            rule.addCreatorColumn("learning_session_test_record");
            rule.addCreatorColumn("learning_word_card");
            rule.addCreatorColumn("learning_word_card_definition");
            rule.addCreatorColumn("learning_word_set");
            rule.addCreatorColumn("learning_word_set_collection");
            rule.addCreatorColumn("learning_word_set_collection_statistic");
            rule.addCreatorColumn("learning_word_set_study_statistic");
            rule.addCreatorColumn("learning_word_set_top");
            // quiz
            rule.addCreatorColumn("quiz_ai_question");
            rule.addCreatorColumn("quiz_ai_question_attachment");
            rule.addCreatorColumn("quiz_ai_question_file_analysis");
            rule.addCreatorColumn("quiz_ai_question_file_analysis_data");
            rule.addCreatorColumn("quiz_ai_question_generate_config");
            rule.addCreatorColumn("quiz_ai_question_generate_task");
            rule.addCreatorColumn("quiz_ai_question_generate_task_record");
            rule.addCreatorColumn("quiz_ai_question_selected_data");
            rule.addCreatorColumn("quiz_ai_question_selected_data_keyword");
            rule.addCreatorColumn("quiz_question_bank");
            rule.addCreatorColumn("quiz_question_bank_knowledge_point");
            rule.addCreatorColumn("quiz_question_bank_option");
            rule.addCreatorColumn("quiz_question_bank_tag");
        };
    }

}
