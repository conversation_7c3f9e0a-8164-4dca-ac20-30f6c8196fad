package com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程收藏 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonCollectionRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20838")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23083")
    @ExcelProperty("课程编号")
    private Long lessonId;

    @Schema(description = "用户编号", deprecated = true)
    @Deprecated
    private Long userId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}