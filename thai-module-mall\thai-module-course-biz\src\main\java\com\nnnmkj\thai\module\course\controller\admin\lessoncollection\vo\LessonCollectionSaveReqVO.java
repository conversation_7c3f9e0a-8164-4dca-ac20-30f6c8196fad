package com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 课程收藏新增/修改 Request VO")
@Data
public class LessonCollectionSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20838")
    private Long id;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23083")
    @NotNull(message = "课程编号不能为空")
    private Long lessonId;

    @Schema(description = "用户编号", deprecated = true)
    @Deprecated
    private Long userId;

}