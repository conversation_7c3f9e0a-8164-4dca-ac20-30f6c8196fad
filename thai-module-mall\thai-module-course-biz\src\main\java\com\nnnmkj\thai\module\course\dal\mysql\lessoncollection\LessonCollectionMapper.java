package com.nnnmkj.thai.module.course.dal.mysql.lessoncollection;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lessoncollection.vo.LessonCollectionPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程收藏 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonCollectionMapper extends BaseMapperX<LessonCollectionDO> {

    default PageResult<LessonCollectionDO> selectPage(LessonCollectionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonCollectionDO>()
                .eqIfPresent(LessonCollectionDO::getLessonId, reqVO.getLessonId())
                .betweenIfPresent(LessonCollectionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonCollectionDO::getId));
    }

}