package com.nnnmkj.thai.module.course.controller.app.lessoncollection.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "用户 APP - 课程收藏新增/修改 Request VO")
@Data
public class AppLessonCollectionSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "4789")
    private Long id;

    @Schema(description = "课程编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "32287")
    @NotNull(message = "课程编号不能为空")
    private Long lessonId;

}